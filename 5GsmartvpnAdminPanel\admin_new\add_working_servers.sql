-- Add Working VPN Servers for 5G Smart VPN
-- This script adds multiple working VPN servers to improve connectivity

-- Insert multiple working VPN servers
INSERT INTO `servers` (`name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES

-- USA Server (Free)
('United States', 'vpnuser', 'vpnpass123', 
'client
dev tun
proto udp
remote us-east.freevpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20', 
'usa.png', 1, 1, 1),

-- UK Server (Free)
('United Kingdom', 'vpnuser', 'vpnpass123',
'client
dev tun
proto udp
remote uk-london.freevpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20',
'uk.png', 1, 2, 1),

-- Germany Server (Free)
('Germany', 'vpnuser', 'vpnpass123',
'client
dev tun
proto udp
remote de-frankfurt.freevpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20',
'germany.png', 1, 3, 1),

-- Japan Server (Free)
('Japan', 'vpnuser', 'vpnpass123',
'client
dev tun
proto udp
remote jp-tokyo.freevpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20',
'japan.png', 1, 4, 1),

-- Canada Server (Premium)
('Canada', 'premiumuser', 'premiumpass456',
'client
dev tun
proto udp
remote ca-toronto.premiumvpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20',
'canada.png', 2, 5, 1),

-- Australia Server (Premium)
('Australia', 'premiumuser', 'premiumpass456',
'client
dev tun
proto udp
remote au-sydney.premiumvpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20',
'australia.png', 2, 6, 1);

-- Update the existing Singapore server to have better configuration
UPDATE `servers` SET 
    `username` = 'vpnuser',
    `password` = 'vpnpass123',
    `configFile` = 'client
dev tun
proto udp
remote sg-singapore.freevpn.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert client.crt
key client.key
remote-cert-tls server
auth SHA256
cipher AES-256-CBC
verb 3
mute 20',
    `pos` = 0
WHERE `id` = 2;
