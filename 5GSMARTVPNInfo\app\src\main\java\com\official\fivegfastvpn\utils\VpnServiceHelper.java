package com.official.fivegfastvpn.utils;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.VpnService;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import java.util.concurrent.TimeUnit;

import de.blinkt.openvpn.OpenVpnApi;
import de.blinkt.openvpn.core.VpnStatus;
import de.blinkt.openvpn.core.ConnectionStatus;

/**
 * Helper class to manage VPN connections with improved error handling and timeout management
 */
public class VpnServiceHelper {
    private static final String TAG = "VpnServiceHelper";
    private static final long CONNECTION_TIMEOUT = TimeUnit.SECONDS.toMillis(30); // 30 seconds timeout
    
    private Context context;
    private Handler timeoutHandler;
    private Runnable timeoutRunnable;
    private boolean isConnecting = false;
    
    public interface VpnConnectionCallback {
        void onConnectionSuccess();
        void onConnectionFailed(String error);
        void onConnectionTimeout();
    }
    
    public VpnServiceHelper(Context context) {
        this.context = context;
        this.timeoutHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * Start VPN connection with timeout and error handling
     */
    public void startVpnWithTimeout(String config, String country, String username, String password,
                                   VpnConnectionCallback callback) {
        if (isConnecting) {
            Log.w(TAG, "VPN connection already in progress");
            return;
        }

        isConnecting = true;
        Log.d(TAG, "Starting VPN connection to: " + country);

        // Validate configuration before attempting connection
        if (!validateVpnConfig(config, username, password)) {
            Log.e(TAG, "Invalid VPN configuration");
            isConnecting = false;
            if (callback != null) {
                timeoutHandler.post(() -> callback.onConnectionFailed("Invalid VPN configuration"));
            }
            return;
        }

        // Check network connectivity
        if (!isNetworkAvailable()) {
            Log.e(TAG, "No network connectivity available");
            isConnecting = false;
            if (callback != null) {
                timeoutHandler.post(() -> callback.onConnectionFailed("No internet connection"));
            }
            return;
        }

        // Set up timeout
        timeoutRunnable = () -> {
            Log.e(TAG, "VPN connection timeout after " + CONNECTION_TIMEOUT + "ms");
            isConnecting = false;
            if (callback != null) {
                // Execute callback on main thread
                timeoutHandler.post(() -> callback.onConnectionTimeout());
            }
            // Send timeout state to UI
            sendConnectionState("TIMEOUT");
        };

        timeoutHandler.postDelayed(timeoutRunnable, CONNECTION_TIMEOUT);

        try {
            // Check VPN permission first
            Intent vpnIntent = VpnService.prepare(context);
            if (vpnIntent != null) {
                Log.e(TAG, "VPN permission not granted");
                cancelTimeout();
                isConnecting = false;
                if (callback != null) {
                    // Execute callback on main thread
                    timeoutHandler.post(() -> callback.onConnectionFailed("VPN permission not granted. Please allow VPN access."));
                }
                return;
            }

            // Start VPN connection with enhanced error handling
            try {
                OpenVpnApi.startVpn(context, config, country, username, password);
                Log.d(TAG, "VPN start command sent successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start VPN: " + e.getMessage(), e);
                cancelTimeout();
                isConnecting = false;
                if (callback != null) {
                    timeoutHandler.post(() -> callback.onConnectionFailed("Failed to start VPN: " + e.getMessage()));
                }
                return;
            }
            
            // Monitor connection state
            VpnStatus.StateListener stateListener = new VpnStatus.StateListener() {
                @Override
                public void updateState(String state, String logmessage, int localizedResId,
                                      ConnectionStatus level, Intent intent) {
                    Log.d(TAG, "VPN State: " + state + ", Level: " + level + ", Message: " + logmessage);
                    
                    if (level == ConnectionStatus.LEVEL_CONNECTED) {
                        Log.i(TAG, "VPN connected successfully");
                        cancelTimeout();
                        isConnecting = false;
                        VpnStatus.removeStateListener(this);
                        if (callback != null) {
                            // Execute callback on main thread
                            timeoutHandler.post(() -> callback.onConnectionSuccess());
                        }
                    } else if (level == ConnectionStatus.LEVEL_AUTH_FAILED ||
                              level == ConnectionStatus.LEVEL_NOTCONNECTED) {
                        Log.e(TAG, "VPN connection failed: " + logmessage);
                        cancelTimeout();
                        isConnecting = false;
                        VpnStatus.removeStateListener(this);
                        if (callback != null) {
                            // Execute callback on main thread
                            timeoutHandler.post(() -> callback.onConnectionFailed(logmessage));
                        }
                    }
                    
                    // Send state to UI
                    sendConnectionState(state);
                }
                
                @Override
                public void setConnectedVPN(String uuid) {
                    Log.d(TAG, "Connected VPN UUID: " + uuid);
                }
            };
            
            VpnStatus.addStateListener(stateListener);
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting VPN: " + e.getMessage(), e);
            cancelTimeout();
            isConnecting = false;
            if (callback != null) {
                // Execute callback on main thread
                timeoutHandler.post(() -> callback.onConnectionFailed("Error starting VPN: " + e.getMessage()));
            }
        }
    }
    
    /**
     * Cancel the connection timeout
     */
    private void cancelTimeout() {
        if (timeoutRunnable != null) {
            timeoutHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }
    
    /**
     * Send connection state to UI via broadcast
     */
    private void sendConnectionState(String state) {
        Intent intent = new Intent("connectionState");
        intent.putExtra("state", state);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }
    
    /**
     * Check if VPN is currently connecting
     */
    public boolean isConnecting() {
        return isConnecting;
    }
    
    /**
     * Reset connection state
     */
    public void reset() {
        cancelTimeout();
        isConnecting = false;
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        cancelTimeout();
        isConnecting = false;
    }

    /**
     * Validate VPN configuration
     */
    private boolean validateVpnConfig(String config, String username, String password) {
        if (config == null || config.trim().isEmpty()) {
            Log.e(TAG, "VPN config is null or empty");
            return false;
        }

        if (username == null || username.trim().isEmpty()) {
            Log.e(TAG, "VPN username is null or empty");
            return false;
        }

        if (password == null || password.trim().isEmpty()) {
            Log.e(TAG, "VPN password is null or empty");
            return false;
        }

        // Check if config contains essential OpenVPN directives
        if (!config.contains("client") || !config.contains("remote")) {
            Log.e(TAG, "VPN config missing essential directives");
            return false;
        }

        return true;
    }

    /**
     * Check network availability
     */
    private boolean isNetworkAvailable() {
        try {
            ConnectivityManager connectivityManager =
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

            if (connectivityManager == null) {
                return false;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network network = connectivityManager.getActiveNetwork();
                if (network == null) return false;

                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
                return capabilities != null &&
                       (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET));
            } else {
                NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                return networkInfo != null && networkInfo.isConnected();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking network availability", e);
            return false;
        }
    }
}
